{"name": "scopezilla", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "next build", "start": "concurrently --raw 'next dev' 'docker compose up'", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:check": "eslint . --ext .js,.jsx,.ts,.tsx --max-warnings 0", "format": "prettier --write .", "format:check": "prettier --check ."}, "author": "", "license": "ISC", "engines": {"node": "24.2.0", "npm": "11.3.0"}, "devDependencies": {"@types/node": "24.10.0", "@types/react": "^19.2.2", "@types/react-dom": "^19.2.2", "concurrently": "^9.2.1", "eslint-kit": "^11.39.0", "prettier": "^3.6.2", "supabase": "^2.54.11", "typescript": "^5.9.3"}, "dependencies": {"next": "^16.0.1"}}